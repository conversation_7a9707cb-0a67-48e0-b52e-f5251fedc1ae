<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#9333ea;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Rounded rectangle background -->
  <rect x="2" y="2" width="28" height="28" rx="6" ry="6" fill="url(#gradient)"/>
  
  <!-- Refined code brackets with modern styling -->
  <g fill="none" stroke="white" stroke-linecap="round" stroke-linejoin="round">
    <!-- Left bracket < with tighter angle -->
    <path d="M12 12 L9 16 L12 20" stroke-width="2" opacity="0.95"/>

    <!-- Forward slash / with better spacing -->
    <path d="M16 11 L14 21" stroke-width="2" opacity="0.95"/>

    <!-- Right bracket > with tighter angle -->
    <path d="M20 12 L23 16 L20 20" stroke-width="2" opacity="0.95"/>

  </g>
</svg>