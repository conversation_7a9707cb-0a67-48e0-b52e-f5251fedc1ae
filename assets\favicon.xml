<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#9333ea;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Rounded rectangle background -->
  <rect x="2" y="2" width="28" height="28" rx="6" ry="6" fill="url(#gradient)"/>
  
  <!-- Code brackets matching header logo (Code2 from Lucide) -->
  <g fill="none" stroke="white" stroke-linecap="round" stroke-linejoin="round" stroke-width="2.2" opacity="0.95">
    <!-- Right bracket > -->
    <path d="m20 18 3-2-3-2"/>

    <!-- Left bracket < -->
    <path d="m12 14-3 2 3 2"/>

    <!-- Forward slash / -->
    <path d="m17.5 10-3 12"/>
  </g>
</svg>